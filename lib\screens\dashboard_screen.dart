import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/dashboard_data.dart';
import '../widgets/common/stat_card.dart';
import '../widgets/charts/earnings_chart.dart';
import '../widgets/common/transaction_item.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final dashboardData = DashboardData.getMockData();

    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(),
              
              // Stats Grid
              _buildStatsGrid(dashboardData.stats),
              
              const SizedBox(height: AppDimensions.marginL),
              
              // Earnings Chart
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                ),
                child: Earnings<PERSON>hart(data: dashboardData.earningsData),
              ),
              
              const SizedBox(height: AppDimensions.marginL),
              
              // Recent Transactions
              _buildRecentTransactions(dashboardData.recentTransactions),
              
              const SizedBox(height: AppDimensions.marginXL),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Dashboard',
            style: AppTextStyles.h2,
          ),
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.textSecondary,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.person,
              color: AppColors.surface,
              size: AppDimensions.iconM,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid(DashboardStats stats) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: StatCard(
                  title: 'Sales Today',
                  value: '\$${stats.salesToday.toStringAsFixed(0)}',
                  icon: Icons.trending_up,
                  iconColor: AppColors.success,
                  growth: '${stats.salesGrowth.toStringAsFixed(0)}%',
                  isPositiveGrowth: stats.salesGrowth > 0,
                ),
              ),
              const SizedBox(width: AppDimensions.marginM),
              Expanded(
                child: StatCard(
                  title: 'Vouchers Sold',
                  value: '${stats.vouchersSold}',
                  subtitle: '${stats.vouchersGrowth} more than yesterday',
                  icon: Icons.receipt_long,
                  iconColor: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.marginM),
          Row(
            children: [
              Expanded(
                child: StatCard(
                  title: 'Active Users',
                  value: '${stats.activeUsers}',
                  subtitle: 'Currently online',
                  icon: Icons.people,
                  iconColor: AppColors.secondary,
                ),
              ),
              const SizedBox(width: AppDimensions.marginM),
              Expanded(
                child: StatCard(
                  title: 'Available Vouchers',
                  value: '${stats.availableVouchers}',
                  subtitle: '${stats.expiringVouchers} expiring soon',
                  icon: Icons.wifi,
                  iconColor: AppColors.warning,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions(List<Transaction> transactions) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
      ),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Transactions',
                  style: AppTextStyles.h4,
                ),
                Text(
                  'See All',
                  style: AppTextStyles.labelMedium.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: transactions.length,
            separatorBuilder: (context, index) => const Divider(
              color: AppColors.divider,
              height: 1,
              indent: AppDimensions.paddingM,
              endIndent: AppDimensions.paddingM,
            ),
            itemBuilder: (context, index) {
              return TransactionItem(transaction: transactions[index]);
            },
          ),
        ],
      ),
    );
  }
}
