import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/location_sales_data.dart';

class LocationDetailScreen extends StatefulWidget {
  final LocationSales location;

  const LocationDetailScreen({
    super.key,
    required this.location,
  });

  @override
  State<LocationDetailScreen> createState() => _LocationDetailScreenState();
}

class _LocationDetailScreenState extends State<LocationDetailScreen> {
  String _selectedTimeFilter = 'Hourly';
  final List<String> _timeFilters = ['Hourly', 'Daily', 'Weekly'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppDimensions.paddingS),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Total Sales Card
                    _buildTotalSalesCard(),
                    
                    const SizedBox(height: AppDimensions.marginM),

                    // Sales Chart Section
                    _buildSalesChartSection(),

                    const SizedBox(height: AppDimensions.marginM),

                    // WiFi Plans Section
                    _buildWiFiPlansSection(),

                    const SizedBox(height: AppDimensions.marginM),

                    // Top Customers Section
                    _buildTopCustomersSection(),

                    const SizedBox(height: AppDimensions.marginM),

                    // Export Button
                    _buildExportButton(),

                    const SizedBox(height: AppDimensions.marginL),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(
            color: AppColors.divider,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: const Icon(
              Icons.arrow_back,
              color: AppColors.textPrimary,
              size: AppDimensions.iconM,
            ),
          ),
          const SizedBox(width: AppDimensions.marginM),
          Expanded(
            child: Text(
              'Sales Today - ${widget.location.name}',
              style: AppTextStyles.h3,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const Icon(
            Icons.refresh,
            color: AppColors.textSecondary,
            size: AppDimensions.iconM,
          ),
        ],
      ),
    );
  }

  Widget _buildTotalSalesCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingS),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: const Icon(
                  Icons.account_balance_wallet,
                  size: AppDimensions.iconS,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: AppDimensions.marginM),
              Text(
                'Total Sales Today',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginS),
          
          Text(
            widget.location.formattedSales,
            style: AppTextStyles.h1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: AppDimensions.marginS),
          
          Text(
            'As of 4:15 PM',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalesChartSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Sales by Hour',
                  style: AppTextStyles.h4,
                ),
              ),
              const SizedBox(width: AppDimensions.marginS),
              Wrap(
                spacing: AppDimensions.marginS,
                children: _timeFilters.map((filter) {
                  final isSelected = filter == _selectedTimeFilter;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedTimeFilter = filter;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.primary : Colors.transparent,
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      ),
                      child: Text(
                        filter,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: isSelected ? AppColors.surface : AppColors.textSecondary,
                          fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginS),
          
          // Mock Chart
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: CustomPaint(
              painter: _SalesChartPainter(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWiFiPlansSection() {
    final plans = [
      {'name': '24hrs', 'vouchers': 14, 'amount': 14000},
      {'name': '7days', 'vouchers': 8, 'amount': 64000},
      {'name': '30days', 'vouchers': 3, 'amount': 90000},
    ];

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'By WiFi Plan',
            style: AppTextStyles.h4,
          ),
          
          const SizedBox(height: AppDimensions.marginS),
          
          ...plans.map((plan) => _buildPlanItem(
            plan['name'] as String,
            plan['vouchers'] as int,
            plan['amount'] as int,
          )),
        ],
      ),
    );
  }

  Widget _buildPlanItem(String planName, int vouchers, int amount) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginM),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: const Icon(
              Icons.wifi,
              size: AppDimensions.iconS,
              color: AppColors.primary,
            ),
          ),

          const SizedBox(width: AppDimensions.marginM),

          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  planName,
                  style: AppTextStyles.labelMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '$vouchers Vouchers',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          const SizedBox(width: AppDimensions.marginS),

          Flexible(
            flex: 1,
            child: Text(
              'UGX ${amount.toString().replaceAllMapped(
                RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                (Match m) => '${m[1]},',
              )}',
              style: AppTextStyles.labelSmall.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.right,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopCustomersSection() {
    final customers = [
      {'phone': '070****123', 'amount': 8000},
      {'phone': '077****456', 'amount': 8000},
      {'phone': '075****789', 'amount': 30000},
    ];

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Top Customers',
            style: AppTextStyles.h4,
          ),

          const SizedBox(height: AppDimensions.marginS),

          ...customers.map((customer) => _buildCustomerItem(
            customer['phone'] as String,
            customer['amount'] as int,
          )),
        ],
      ),
    );
  }

  Widget _buildCustomerItem(String phone, int amount) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginM),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.textSecondary,
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: Text(
                '0',
                style: TextStyle(
                  color: AppColors.surface,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          const SizedBox(width: AppDimensions.marginM),

          Expanded(
            flex: 2,
            child: Text(
              phone,
              style: AppTextStyles.labelMedium,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          const SizedBox(width: AppDimensions.marginS),

          Flexible(
            flex: 1,
            child: Text(
              'UGX ${amount.toString().replaceAllMapped(
                RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                (Match m) => '${m[1]},',
              )}',
              style: AppTextStyles.labelSmall.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.right,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExportButton() {
    return SizedBox(
      width: double.infinity,
      height: AppDimensions.buttonHeight,
      child: ElevatedButton.icon(
        onPressed: () {
          // TODO: Implement export functionality
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Export functionality will be implemented'),
              backgroundColor: AppColors.primary,
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.surface,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
        icon: const Icon(
          Icons.download,
          size: AppDimensions.iconS,
        ),
        label: Text(
          'Export Sales Report',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.surface,
          ),
        ),
      ),
    );
  }
}

// Custom painter for the sales chart
class _SalesChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary
      ..style = PaintingStyle.fill;

    // Mock chart data (representing hourly sales)
    final data = [2500, 4000, 6500, 5500, 3500, 7000, 6000, 8500, 9000, 7500];
    final barWidth = size.width / (data.length * 1.5);
    final maxValue = data.reduce((a, b) => a > b ? a : b).toDouble();

    for (int i = 0; i < data.length; i++) {
      final barHeight = (data[i] / maxValue) * (size.height - 40);
      final x = (i * barWidth * 1.5) + (barWidth * 0.25);
      final y = size.height - barHeight - 20;

      final rect = Rect.fromLTWH(x, y, barWidth, barHeight);
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(4)),
        paint,
      );
    }

    // Draw time labels
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    final timeLabels = ['9AM', '11AM', '1PM', '3PM', '5PM'];
    for (int i = 0; i < timeLabels.length; i++) {
      textPainter.text = TextSpan(
        text: timeLabels[i],
        style: AppTextStyles.caption.copyWith(
          color: AppColors.textSecondary,
        ),
      );
      textPainter.layout();

      final x = (i * (size.width / (timeLabels.length - 1))) - (textPainter.width / 2);
      textPainter.paint(canvas, Offset(x, size.height - 15));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
