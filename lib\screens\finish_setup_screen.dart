import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/wifi_plan_data.dart';
import 'main_screen.dart';

class FinishSetupScreen extends StatelessWidget {
  final List<WiFiPlan> wifiPlans;

  const FinishSetupScreen({
    super.key,
    required this.wifiPlans,
  });

  void _finishSetup(BuildContext context) {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => const MainScreen(),
      ),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(context),
              
              const SizedBox(height: AppDimensions.marginL),

              // Success Icon and Message
              _buildSuccessSection(),

              const SizedBox(height: AppDimensions.marginL),

              // Setup Summary
              _buildSetupSummary(),

              const SizedBox(height: AppDimensions.marginL),

              // WiFi Plans Summary
              _buildWiFiPlansSummary(),

              const SizedBox(height: AppDimensions.marginL),

              // Next Steps
              _buildNextSteps(),

              const SizedBox(height: AppDimensions.marginL),

              // Finish Button
              _buildFinishButton(context),

              const SizedBox(height: AppDimensions.marginM),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: const Icon(
            Icons.arrow_back,
            color: AppColors.textPrimary,
            size: AppDimensions.iconM,
          ),
        ),
        const SizedBox(width: AppDimensions.marginM),
        Expanded(
          child: Text(
            'Review & Finish',
            style: AppTextStyles.h3,
          ),
        ),
      ],
    );
  }

  Widget _buildSuccessSection() {
    return Center(
      child: Column(
        children: [
          // Success Icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppColors.success.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check_circle,
              size: 48,
              color: AppColors.success,
            ),
          ),
          
          const SizedBox(height: AppDimensions.marginM),

          // Success Message
          Text(
            'You\'re all set to start selling WiFi vouchers!',
            style: AppTextStyles.h3,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppDimensions.marginS),
          
          Text(
            'Your billing system is now configured and ready to use. You can start generating vouchers and tracking sales immediately.',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSetupSummary() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.business,
                size: AppDimensions.iconM,
                color: AppColors.primary,
              ),
              const SizedBox(width: AppDimensions.marginM),
              Text(
                'Business Information',
                style: AppTextStyles.h4,
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginS),
          
          _buildSummaryItem(
            icon: Icons.store,
            label: 'Business Setup',
            value: 'Completed',
            isCompleted: true,
          ),
          
          const SizedBox(height: AppDimensions.marginS),
          
          _buildSummaryItem(
            icon: Icons.wifi,
            label: 'WiFi Plans',
            value: '${wifiPlans.length} plan${wifiPlans.length != 1 ? 's' : ''} configured',
            isCompleted: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem({
    required IconData icon,
    required String label,
    required String value,
    required bool isCompleted,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: AppDimensions.iconS,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: AppDimensions.marginM),
        Expanded(
          child: Text(
            label,
            style: AppTextStyles.bodyMedium,
          ),
        ),
        Row(
          children: [
            if (isCompleted)
              const Icon(
                Icons.check_circle,
                size: AppDimensions.iconS,
                color: AppColors.success,
              ),
            const SizedBox(width: AppDimensions.marginS),
            Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                color: isCompleted ? AppColors.success : AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWiFiPlansSummary() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.wifi,
                size: AppDimensions.iconM,
                color: AppColors.primary,
              ),
              const SizedBox(width: AppDimensions.marginM),
              Text(
                'WiFi Plans (${wifiPlans.length})',
                style: AppTextStyles.h4,
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginM),
          
          ...wifiPlans.take(3).map((plan) => _buildPlanSummaryItem(plan)),
          
          if (wifiPlans.length > 3)
            Padding(
              padding: const EdgeInsets.only(top: AppDimensions.marginS),
              child: Text(
                '+ ${wifiPlans.length - 3} more plan${wifiPlans.length - 3 != 1 ? 's' : ''}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPlanSummaryItem(WiFiPlan plan) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginS),
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  plan.name,
                  style: AppTextStyles.labelMedium,
                ),
                const SizedBox(height: AppDimensions.marginXS),
                Text(
                  '${plan.durationDisplay} • ${plan.dataLimitDisplay}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '\$${plan.price.toStringAsFixed(2)}',
            style: AppTextStyles.labelMedium.copyWith(
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextSteps() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.lightbulb_outline,
                size: AppDimensions.iconM,
                color: AppColors.primary,
              ),
              const SizedBox(width: AppDimensions.marginM),
              Text(
                'What\'s Next?',
                style: AppTextStyles.h4,
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginM),
          
          _buildNextStepItem(
            '1. Start generating vouchers from the Vouchers tab',
          ),
          _buildNextStepItem(
            '2. Monitor your sales and analytics on the Dashboard',
          ),
          _buildNextStepItem(
            '3. Track all transactions in the Transactions tab',
          ),
          _buildNextStepItem(
            '4. Adjust settings and plans anytime in Settings',
          ),
        ],
      ),
    );
  }

  Widget _buildNextStepItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.marginS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.check,
            size: AppDimensions.iconS,
            color: AppColors.primary,
          ),
          const SizedBox(width: AppDimensions.marginM),
          Expanded(
            child: Text(
              text,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinishButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: AppDimensions.buttonHeight,
      child: ElevatedButton(
        onPressed: () => _finishSetup(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.surface,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
        child: Text(
          'Finish Setup',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.surface,
          ),
        ),
      ),
    );
  }
}
