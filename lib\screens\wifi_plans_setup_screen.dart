import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/wifi_plan_data.dart';
import 'main_screen.dart';

class WiFiPlansSetupScreen extends StatefulWidget {
  const WiFiPlansSetupScreen({super.key});

  @override
  State<WiFiPlansSetupScreen> createState() => _WiFiPlansSetupScreenState();
}

class _WiFiPlansSetupScreenState extends State<WiFiPlansSetupScreen> {
  final TextEditingController _planNameController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _durationController = TextEditingController();
  final TextEditingController _dataLimitController = TextEditingController();
  String _durationUnit = 'hours';
  List<WiFiPlan> _wifiPlans = [];

  @override
  void initState() {
    super.initState();
    _wifiPlans = WiFiPlanData.getDefaultPlans();
  }

  @override
  void dispose() {
    _planNameController.dispose();
    _priceController.dispose();
    _durationController.dispose();
    _dataLimitController.dispose();
    super.dispose();
  }

  void _addPlan() {
    if (_planNameController.text.isEmpty ||
        _priceController.text.isEmpty ||
        _durationController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all required fields'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final newPlan = WiFiPlan(
      id: 'plan_${DateTime.now().millisecondsSinceEpoch}',
      name: _planNameController.text,
      price: double.tryParse(_priceController.text) ?? 0.0,
      duration: int.tryParse(_durationController.text) ?? 1,
      durationUnit: _durationUnit,
      dataLimit: _dataLimitController.text.isEmpty ? null : _dataLimitController.text,
    );

    setState(() {
      _wifiPlans.add(newPlan);
    });

    _clearForm();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Plan added successfully'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _clearForm() {
    _planNameController.clear();
    _priceController.clear();
    _durationController.clear();
    _dataLimitController.clear();
    setState(() {
      _durationUnit = 'hours';
    });
  }

  void _editPlan(WiFiPlan plan) {
    // TODO: Implement edit plan functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit ${plan.name} functionality will be implemented'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _deletePlan(WiFiPlan plan) {
    setState(() {
      _wifiPlans.removeWhere((p) => p.id == plan.id);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${plan.name} deleted'),
        backgroundColor: AppColors.error,
      ),
    );
  }

  void _goToNextStep() {
    // Navigate to payment setup or main screen
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const MainScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(),
              
              const SizedBox(height: AppDimensions.marginXL),
              
              // Plan Creation Form
              _buildPlanForm(),
              
              const SizedBox(height: AppDimensions.marginL),
              
              // Add Plan Button
              _buildAddPlanButton(),
              
              const SizedBox(height: AppDimensions.marginXL),
              
              // Your Plans Section
              _buildPlansSection(),
              
              const SizedBox(height: AppDimensions.marginXL),
              
              // Next Button
              _buildNextButton(),
              
              const SizedBox(height: AppDimensions.marginXL),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: const Icon(
            Icons.arrow_back,
            color: AppColors.textPrimary,
            size: AppDimensions.iconM,
          ),
        ),
        const SizedBox(width: AppDimensions.marginM),
        Expanded(
          child: Text(
            'Create Your WiFi Plans',
            style: AppTextStyles.h3,
          ),
        ),
      ],
    );
  }

  Widget _buildPlanForm() {
    return Column(
      children: [
        // Plan Name
        _buildFormField(
          label: 'Plan Name',
          controller: _planNameController,
          placeholder: 'e.g., Basic 1hr',
        ),
        
        const SizedBox(height: AppDimensions.marginL),
        
        // Price
        _buildFormField(
          label: 'Price',
          controller: _priceController,
          placeholder: '0.00',
          prefix: '\$',
          keyboardType: TextInputType.number,
        ),
        
        const SizedBox(height: AppDimensions.marginL),
        
        // Duration
        _buildDurationField(),
        
        const SizedBox(height: AppDimensions.marginL),
        
        // Data Limit
        _buildFormField(
          label: 'Data Limit (Optional)',
          controller: _dataLimitController,
          placeholder: 'e.g., 1GB, unlimited',
        ),
      ],
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String placeholder,
    String? prefix,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.labelMedium,
        ),
        const SizedBox(height: AppDimensions.marginS),
        Container(
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: AppColors.divider,
              width: 1,
            ),
          ),
          child: TextField(
            controller: controller,
            keyboardType: keyboardType,
            style: AppTextStyles.bodyMedium,
            decoration: InputDecoration(
              hintText: placeholder,
              hintStyle: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              prefixText: prefix,
              prefixStyle: AppTextStyles.bodyMedium,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(AppDimensions.paddingM),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDurationField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Duration',
          style: AppTextStyles.labelMedium,
        ),
        const SizedBox(height: AppDimensions.marginS),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: AppColors.divider,
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: _durationController,
                  keyboardType: TextInputType.number,
                  style: AppTextStyles.bodyMedium,
                  decoration: InputDecoration(
                    hintText: '1',
                    hintStyle: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(AppDimensions.paddingM),
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppDimensions.marginM),
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: AppColors.divider,
                    width: 1,
                  ),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _durationUnit,
                    onChanged: (String? newValue) {
                      setState(() {
                        _durationUnit = newValue!;
                      });
                    },
                    items: <String>['hours', 'days', 'weeks', 'months']
                        .map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
                          child: Text(value, style: AppTextStyles.bodyMedium),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAddPlanButton() {
    return SizedBox(
      width: double.infinity,
      height: AppDimensions.buttonHeight,
      child: ElevatedButton(
        onPressed: _addPlan,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.surface,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
        child: Text(
          'Add Plan',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.surface,
          ),
        ),
      ),
    );
  }

  Widget _buildPlansSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Plans',
          style: AppTextStyles.h4,
        ),
        const SizedBox(height: AppDimensions.marginL),
        ..._wifiPlans.map((plan) => _buildPlanItem(plan)).toList(),
      ],
    );
  }

  Widget _buildPlanItem(WiFiPlan plan) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginM),
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  plan.name,
                  style: AppTextStyles.labelLarge,
                ),
                const SizedBox(height: AppDimensions.marginXS),
                Text(
                  '\$${plan.price.toStringAsFixed(2)}',
                  style: AppTextStyles.bodyMedium,
                ),
                const SizedBox(height: AppDimensions.marginXS),
                Text(
                  plan.durationDisplay,
                  style: AppTextStyles.bodySmall,
                ),
                const SizedBox(height: AppDimensions.marginXS),
                Text(
                  plan.dataLimitDisplay,
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ),
          Row(
            children: [
              GestureDetector(
                onTap: () => _editPlan(plan),
                child: Container(
                  padding: const EdgeInsets.all(AppDimensions.paddingS),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  ),
                  child: const Icon(
                    Icons.edit_outlined,
                    size: AppDimensions.iconS,
                    color: AppColors.primary,
                  ),
                ),
              ),
              const SizedBox(width: AppDimensions.marginS),
              GestureDetector(
                onTap: () => _deletePlan(plan),
                child: Container(
                  padding: const EdgeInsets.all(AppDimensions.paddingS),
                  decoration: BoxDecoration(
                    color: AppColors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  ),
                  child: const Icon(
                    Icons.delete_outline,
                    size: AppDimensions.iconS,
                    color: AppColors.error,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNextButton() {
    return SizedBox(
      width: double.infinity,
      height: AppDimensions.buttonHeight,
      child: ElevatedButton(
        onPressed: _goToNextStep,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.surface,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
        child: Text(
          'Next',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.surface,
          ),
        ),
      ),
    );
  }
}
