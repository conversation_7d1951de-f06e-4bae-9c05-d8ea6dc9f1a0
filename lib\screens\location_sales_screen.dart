import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/location_sales_data.dart';

class LocationSalesScreen extends StatefulWidget {
  const LocationSalesScreen({super.key});

  @override
  State<LocationSalesScreen> createState() => _LocationSalesScreenState();
}

class _LocationSalesScreenState extends State<LocationSalesScreen> {
  List<LocationSales> _locations = [];
  String? _selectedLocationId;

  @override
  void initState() {
    super.initState();
    _locations = LocationSalesData.getMockData();
    _selectedLocationId = LocationSalesData.getSelectedLocation()?.id;
  }

  void _onLocationSelected(String locationId) {
    setState(() {
      _selectedLocationId = locationId;
      _locations = _locations.map((location) {
        return location.copyWith(
          isSelected: location.id == locationId,
        );
      }).toList();
    });

    // Navigate back with selected location
    Navigator.of(context).pop(_locations.firstWhere(
      (location) => location.id == locationId,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            const SizedBox(height: AppDimensions.marginL),
            
            // Locations Grid
            Expanded(
              child: _buildLocationsGrid(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: const Icon(
              Icons.arrow_back,
              color: AppColors.textPrimary,
              size: AppDimensions.iconM,
            ),
          ),
          const SizedBox(width: AppDimensions.marginM),
          Expanded(
            child: Text(
              'Select Location for Sales',
              style: AppTextStyles.h3,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationsGrid() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: AppDimensions.marginM,
          mainAxisSpacing: AppDimensions.marginM,
          childAspectRatio: 0.85,
        ),
        itemCount: _locations.length,
        itemBuilder: (context, index) {
          final location = _locations[index];
          return _buildLocationCard(location);
        },
      ),
    );
  }

  Widget _buildLocationCard(LocationSales location) {
    final isSelected = location.id == _selectedLocationId;
    
    return GestureDetector(
      onTap: () => _onLocationSelected(location.id),
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.divider,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Location Icon and Selection Indicator
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Container(
                    padding: const EdgeInsets.all(AppDimensions.paddingS),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    ),
                    child: const Icon(
                      Icons.location_on,
                      size: AppDimensions.iconS,
                      color: AppColors.primary,
                    ),
                  ),
                ),
                if (isSelected)
                  Container(
                    padding: const EdgeInsets.all(AppDimensions.paddingXS),
                    decoration: const BoxDecoration(
                      color: AppColors.success,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      size: AppDimensions.iconXS,
                      color: AppColors.surface,
                    ),
                  ),
              ],
            ),

            const SizedBox(height: AppDimensions.marginM),

            // Location Name
            Flexible(
              child: Text(
                location.name,
                style: AppTextStyles.labelLarge,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            const SizedBox(height: AppDimensions.marginS),

            // Sales Label
            Text(
              'Sales Today:',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),

            const SizedBox(height: AppDimensions.marginXS),

            // Sales Amount
            Flexible(
              child: Text(
                location.formattedSales,
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
