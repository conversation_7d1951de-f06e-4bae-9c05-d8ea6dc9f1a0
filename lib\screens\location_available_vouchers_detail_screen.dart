import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/available_vouchers_data.dart';

class LocationAvailableVouchersDetailScreen extends StatefulWidget {
  final LocationAvailableVouchers location;

  const LocationAvailableVouchersDetailScreen({
    super.key,
    required this.location,
  });

  @override
  State<LocationAvailableVouchersDetailScreen> createState() => _LocationAvailableVouchersDetailScreenState();
}

class _LocationAvailableVouchersDetailScreenState extends State<LocationAvailableVouchersDetailScreen> {
  String _selectedFilter = 'All';
  final List<String> _filters = ['All', 'Active', 'Expiring'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Available Vouchers Count Card
                    _buildAvailableVouchersCard(),
                    
                    const SizedBox(height: AppDimensions.marginL),
                    
                    // Voucher Status Overview
                    _buildStatusOverviewSection(),
                    
                    const SizedBox(height: AppDimensions.marginL),
                    
                    // Voucher Types Breakdown
                    _buildVoucherTypesSection(),
                    
                    const SizedBox(height: AppDimensions.marginL),
                    
                    // Voucher List
                    _buildVoucherListSection(),
                    
                    const SizedBox(height: AppDimensions.marginL),
                    
                    // Action Buttons
                    _buildActionButtons(),
                    
                    const SizedBox(height: AppDimensions.marginXL),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(
            color: AppColors.divider,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: const Icon(
              Icons.arrow_back,
              color: AppColors.textPrimary,
              size: AppDimensions.iconM,
            ),
          ),
          const SizedBox(width: AppDimensions.marginM),
          Expanded(
            child: Text(
              'Available Vouchers - ${widget.location.name}',
              style: AppTextStyles.h3,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const Icon(
            Icons.wifi,
            color: AppColors.secondary,
            size: AppDimensions.iconM,
          ),
        ],
      ),
    );
  }

  Widget _buildAvailableVouchersCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${widget.location.availableVouchers}',
                    style: AppTextStyles.h1.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 48,
                    ),
                  ),
                  Text(
                    'Available Vouchers',
                    style: AppTextStyles.labelMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingS,
                ),
                decoration: BoxDecoration(
                  color: AppColors.secondary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
                child: Text(
                  'In Stock',
                  style: AppTextStyles.labelSmall.copyWith(
                    color: AppColors.secondary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginS),
          
          if (widget.location.expiringVouchers > 0)
            Row(
              children: [
                Icon(
                  Icons.warning,
                  size: AppDimensions.iconXS,
                  color: AppColors.error,
                ),
                const SizedBox(width: AppDimensions.marginXS),
                Text(
                  '${widget.location.expiringVouchers} vouchers expiring soon',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.error,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildStatusOverviewSection() {
    final activeVouchers = widget.location.availableVouchers - widget.location.expiringVouchers;
    
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Status Overview',
            style: AppTextStyles.h4,
          ),
          
          const SizedBox(height: AppDimensions.marginL),
          
          Row(
            children: [
              Expanded(
                child: _buildStatusCard(
                  'Active',
                  activeVouchers.toString(),
                  AppColors.success,
                  Icons.check_circle,
                ),
              ),
              const SizedBox(width: AppDimensions.marginM),
              Expanded(
                child: _buildStatusCard(
                  'Expiring Soon',
                  widget.location.expiringVouchers.toString(),
                  AppColors.error,
                  Icons.warning,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCard(String title, String count, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: AppDimensions.iconM,
          ),
          const SizedBox(height: AppDimensions.marginS),
          Text(
            count,
            style: AppTextStyles.h3.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildVoucherTypesSection() {
    final voucherTypes = [
      {'type': '24 Hours', 'available': 8, 'expiring': 1},
      {'type': '7 Days', 'available': 6, 'expiring': 0},
      {'type': '30 Days', 'available': 4, 'expiring': 0},
    ];

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Voucher Types',
            style: AppTextStyles.h4,
          ),
          
          const SizedBox(height: AppDimensions.marginL),
          
          ...voucherTypes.map((type) => _buildVoucherTypeItem(
            type['type'] as String,
            type['available'] as int,
            type['expiring'] as int,
          )),
        ],
      ),
    );
  }

  Widget _buildVoucherTypeItem(String type, int available, int expiring) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginM),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: AppColors.secondary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: const Icon(
              Icons.confirmation_number,
              size: AppDimensions.iconS,
              color: AppColors.secondary,
            ),
          ),

          const SizedBox(width: AppDimensions.marginM),

          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type,
                  style: AppTextStyles.labelMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '$available available',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          const SizedBox(width: AppDimensions.marginS),

          if (expiring > 0)
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingS,
                vertical: AppDimensions.paddingXS,
              ),
              decoration: BoxDecoration(
                color: AppColors.error.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Text(
                '$expiring expiring',
                style: AppTextStyles.caption.copyWith(
                  color: AppColors.error,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildVoucherListSection() {
    final vouchers = [
      {'code': 'VOU-456789', 'type': '24hrs', 'status': 'active', 'expires': '2 days'},
      {'code': 'VOU-456790', 'type': '7days', 'status': 'active', 'expires': '5 days'},
      {'code': 'VOU-456791', 'type': '24hrs', 'status': 'expiring', 'expires': '6 hours'},
      {'code': 'VOU-456792', 'type': '30days', 'status': 'active', 'expires': '15 days'},
      {'code': 'VOU-456793', 'type': '7days', 'status': 'active', 'expires': '3 days'},
    ];

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Voucher List',
                  style: AppTextStyles.h4,
                ),
              ),
              const SizedBox(width: AppDimensions.marginS),
              Wrap(
                spacing: AppDimensions.marginS,
                children: _filters.map((filter) {
                  final isSelected = filter == _selectedFilter;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedFilter = filter;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.secondary : Colors.transparent,
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      ),
                      child: Text(
                        filter,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: isSelected ? AppColors.surface : AppColors.textSecondary,
                          fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),

          const SizedBox(height: AppDimensions.marginL),

          ...vouchers.map((voucher) => _buildVoucherItem(
            voucher['code'] as String,
            voucher['type'] as String,
            voucher['status'] as String,
            voucher['expires'] as String,
          )),
        ],
      ),
    );
  }

  Widget _buildVoucherItem(String code, String type, String status, String expires) {
    Color statusColor = status == 'expiring' ? AppColors.error : AppColors.success;

    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginM),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              status == 'expiring' ? Icons.warning : Icons.check_circle,
              color: statusColor,
              size: AppDimensions.iconS,
            ),
          ),

          const SizedBox(width: AppDimensions.marginM),

          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  code,
                  style: AppTextStyles.labelMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '$type • Expires in $expires',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          const SizedBox(width: AppDimensions.marginS),

          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingS,
              vertical: AppDimensions.paddingXS,
            ),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Text(
              status == 'expiring' ? 'Expiring' : 'Active',
              style: AppTextStyles.caption.copyWith(
                color: statusColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: ElevatedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Generate vouchers functionality will be implemented'),
                  backgroundColor: AppColors.secondary,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.secondary,
              foregroundColor: AppColors.surface,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            icon: const Icon(
              Icons.add,
              size: AppDimensions.iconS,
            ),
            label: Text(
              'Generate New Vouchers',
              style: AppTextStyles.labelLarge.copyWith(
                color: AppColors.surface,
              ),
            ),
          ),
        ),

        const SizedBox(height: AppDimensions.marginM),

        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: OutlinedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Export voucher list functionality will be implemented'),
                  backgroundColor: AppColors.textSecondary,
                ),
              );
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.textPrimary,
              side: const BorderSide(
                color: AppColors.divider,
                width: 1,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            icon: const Icon(
              Icons.download,
              size: AppDimensions.iconS,
            ),
            label: Text(
              'Export Voucher List',
              style: AppTextStyles.labelLarge.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
