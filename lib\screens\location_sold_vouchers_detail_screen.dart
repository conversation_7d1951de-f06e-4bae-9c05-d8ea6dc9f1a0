import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/sold_vouchers_data.dart';

class LocationSoldVouchersDetailScreen extends StatefulWidget {
  final LocationSoldVouchers location;

  const LocationSoldVouchersDetailScreen({
    super.key,
    required this.location,
  });

  @override
  State<LocationSoldVouchersDetailScreen> createState() => _LocationSoldVouchersDetailScreenState();
}

class _LocationSoldVouchersDetailScreenState extends State<LocationSoldVouchersDetailScreen> {
  String _selectedTimeFilter = 'Daily';
  final List<String> _timeFilters = ['Daily', 'Weekly', 'Monthly'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Sold Vouchers Count Card
                    _buildSoldVouchersCard(),
                    
                    const SizedBox(height: AppDimensions.marginL),
                    
                    // Sales Timeline Chart
                    _buildSalesTimelineSection(),
                    
                    const SizedBox(height: AppDimensions.marginL),
                    
                    // Voucher Types Breakdown
                    _buildVoucherTypesSection(),
                    
                    const SizedBox(height: AppDimensions.marginL),
                    
                    // Recent Sales
                    _buildRecentSalesSection(),
                    
                    const SizedBox(height: AppDimensions.marginL),
                    
                    // Action Buttons
                    _buildActionButtons(),
                    
                    const SizedBox(height: AppDimensions.marginXL),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(
            color: AppColors.divider,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: const Icon(
              Icons.arrow_back,
              color: AppColors.textPrimary,
              size: AppDimensions.iconM,
            ),
          ),
          const SizedBox(width: AppDimensions.marginM),
          Expanded(
            child: Text(
              'Sold Vouchers - ${widget.location.name}',
              style: AppTextStyles.h3,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const Icon(
            Icons.receipt_long,
            color: AppColors.primary,
            size: AppDimensions.iconM,
          ),
        ],
      ),
    );
  }

  Widget _buildSoldVouchersCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${widget.location.vouchersSold}',
                    style: AppTextStyles.h1.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 48,
                    ),
                  ),
                  Text(
                    'Vouchers Sold Today',
                    style: AppTextStyles.labelMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingS,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
                child: Text(
                  'Today',
                  style: AppTextStyles.labelSmall.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginS),
          
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'Updated 3 mins ago',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalesTimelineSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Sales Timeline',
                  style: AppTextStyles.h4,
                ),
              ),
              const SizedBox(width: AppDimensions.marginS),
              Wrap(
                spacing: AppDimensions.marginS,
                children: _timeFilters.map((filter) {
                  final isSelected = filter == _selectedTimeFilter;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedTimeFilter = filter;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.primary : Colors.transparent,
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      ),
                      child: Text(
                        filter,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: isSelected ? AppColors.surface : AppColors.textSecondary,
                          fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginL),
          
          // Mock Chart
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: CustomPaint(
              painter: _SalesChartPainter(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVoucherTypesSection() {
    final voucherTypes = [
      {'type': '24 Hours', 'sold': 3, 'revenue': 'UGX 15,000'},
      {'type': '7 Days', 'sold': 2, 'revenue': 'UGX 16,000'},
      {'type': '30 Days', 'sold': 1, 'revenue': 'UGX 30,000'},
    ];

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Voucher Types Sold',
            style: AppTextStyles.h4,
          ),
          
          const SizedBox(height: AppDimensions.marginL),
          
          ...voucherTypes.map((type) => _buildVoucherTypeItem(
            type['type'] as String,
            type['sold'] as int,
            type['revenue'] as String,
          )),
        ],
      ),
    );
  }

  Widget _buildVoucherTypeItem(String type, int sold, String revenue) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginM),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: const Icon(
              Icons.confirmation_number,
              size: AppDimensions.iconS,
              color: AppColors.primary,
            ),
          ),

          const SizedBox(width: AppDimensions.marginM),

          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type,
                  style: AppTextStyles.labelMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '$sold sold',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          const SizedBox(width: AppDimensions.marginS),

          Flexible(
            flex: 1,
            child: Text(
              revenue,
              style: AppTextStyles.labelSmall.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.right,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentSalesSection() {
    final recentSales = [
      {'voucher': 'VOU-789123', 'type': '24hrs', 'time': '2 mins ago', 'amount': 'UGX 5,000'},
      {'voucher': 'VOU-789124', 'type': '7days', 'time': '5 mins ago', 'amount': 'UGX 8,000'},
      {'voucher': 'VOU-789125', 'type': '30days', 'time': '8 mins ago', 'amount': 'UGX 30,000'},
      {'voucher': 'VOU-789126', 'type': '24hrs', 'time': '12 mins ago', 'amount': 'UGX 5,000'},
    ];

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Sales',
            style: AppTextStyles.h4,
          ),

          const SizedBox(height: AppDimensions.marginL),

          ...recentSales.map((sale) => _buildRecentSaleItem(
            sale['voucher'] as String,
            sale['type'] as String,
            sale['time'] as String,
            sale['amount'] as String,
          )),
        ],
      ),
    );
  }

  Widget _buildRecentSaleItem(String voucher, String type, String time, String amount) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginM),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.success.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check_circle,
              color: AppColors.success,
              size: AppDimensions.iconS,
            ),
          ),

          const SizedBox(width: AppDimensions.marginM),

          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  voucher,
                  style: AppTextStyles.labelMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '$type • $time',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          const SizedBox(width: AppDimensions.marginS),

          Flexible(
            flex: 1,
            child: Text(
              amount,
              style: AppTextStyles.labelSmall.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.right,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: ElevatedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Export sales report functionality will be implemented'),
                  backgroundColor: AppColors.primary,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.surface,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            icon: const Icon(
              Icons.download,
              size: AppDimensions.iconS,
            ),
            label: Text(
              'Export Sales Report',
              style: AppTextStyles.labelLarge.copyWith(
                color: AppColors.surface,
              ),
            ),
          ),
        ),

        const SizedBox(height: AppDimensions.marginM),

        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: OutlinedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('View transaction history functionality will be implemented'),
                  backgroundColor: AppColors.textSecondary,
                ),
              );
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.textPrimary,
              side: const BorderSide(
                color: AppColors.divider,
                width: 1,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            icon: const Icon(
              Icons.history,
              size: AppDimensions.iconS,
            ),
            label: Text(
              'View Transaction History',
              style: AppTextStyles.labelLarge.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Custom painter for the sales chart
class _SalesChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary
      ..style = PaintingStyle.fill;

    // Mock chart data (representing hourly sales)
    final data = [1, 2, 1, 3, 2, 4, 3, 2, 1];
    final barWidth = size.width / (data.length * 1.5);
    final maxValue = data.reduce((a, b) => a > b ? a : b).toDouble();

    for (int i = 0; i < data.length; i++) {
      final barHeight = (data[i] / maxValue) * (size.height - 40);
      final x = (i * barWidth * 1.5) + (barWidth * 0.25);
      final y = size.height - barHeight - 20;

      final rect = Rect.fromLTWH(x, y, barWidth, barHeight);
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(4)),
        paint,
      );
    }

    // Draw time labels
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    final timeLabels = ['9AM', '11AM', '1PM', '3PM', '5PM'];
    for (int i = 0; i < timeLabels.length; i++) {
      textPainter.text = TextSpan(
        text: timeLabels[i],
        style: AppTextStyles.caption.copyWith(
          color: AppColors.textSecondary,
        ),
      );
      textPainter.layout();

      final x = (i * (size.width / (timeLabels.length - 1))) - (textPainter.width / 2);
      textPainter.paint(canvas, Offset(x, size.height - 15));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
