import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/active_users_data.dart';

class LocationActiveUsersDetailScreen extends StatefulWidget {
  final LocationActiveUsers location;

  const LocationActiveUsersDetailScreen({
    super.key,
    required this.location,
  });

  @override
  State<LocationActiveUsersDetailScreen> createState() => _LocationActiveUsersDetailScreenState();
}

class _LocationActiveUsersDetailScreenState extends State<LocationActiveUsersDetailScreen> {
  String _selectedTimeFilter = 'Daily';
  final List<String> _timeFilters = ['Daily', 'Weekly', 'Monthly'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Active Users Count Card
                    _buildActiveUsersCard(),
                    
                    const SizedBox(height: AppDimensions.marginL),
                    
                    // User Activity Timeline Chart
                    _buildActivityTimelineSection(),
                    
                    const SizedBox(height: AppDimensions.marginL),
                    
                    // Detailed Breakdown
                    _buildDetailedBreakdownSection(),
                    
                    const SizedBox(height: AppDimensions.marginL),
                    
                    // Action Buttons
                    _buildActionButtons(),
                    
                    const SizedBox(height: AppDimensions.marginXL),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(
            color: AppColors.divider,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: const Icon(
              Icons.arrow_back,
              color: AppColors.textPrimary,
              size: AppDimensions.iconM,
            ),
          ),
          const SizedBox(width: AppDimensions.marginM),
          Expanded(
            child: Text(
              'Active Users - ${widget.location.name} Site',
              style: AppTextStyles.h3,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const Icon(
            Icons.wifi,
            color: AppColors.primary,
            size: AppDimensions.iconM,
          ),
        ],
      ),
    );
  }

  Widget _buildActiveUsersCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${widget.location.activeUsers}',
                    style: AppTextStyles.h1.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 48,
                    ),
                  ),
                  Text(
                    'Users Online',
                    style: AppTextStyles.labelMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingS,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
                child: Text(
                  'Today',
                  style: AppTextStyles.labelSmall.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginS),
          
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'Updated 5 mins ago',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityTimelineSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'User Activity Timeline',
                  style: AppTextStyles.h4,
                ),
              ),
              const SizedBox(width: AppDimensions.marginS),
              Wrap(
                spacing: AppDimensions.marginS,
                children: _timeFilters.map((filter) {
                  final isSelected = filter == _selectedTimeFilter;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedTimeFilter = filter;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.primary : Colors.transparent,
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      ),
                      child: Text(
                        filter,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: isSelected ? AppColors.surface : AppColors.textSecondary,
                          fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginL),
          
          // Mock Chart
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: CustomPaint(
              painter: _ActivityChartPainter(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedBreakdownSection() {
    final users = [
      {'voucher': 'VOU-573921', 'timeLeft': '2h:Left', 'phone': '0700869468', 'status': 'active'},
      {'voucher': 'VOU-573922', 'timeLeft': '4h:Left', 'phone': '0700987645', 'status': 'active'},
      {'voucher': 'VOU-573923', 'timeLeft': '3h:Left', 'phone': '0700543216', 'status': 'warning'},
      {'voucher': 'VOU-573924', 'timeLeft': '1h:Left', 'phone': '0700123456', 'status': 'critical'},
      {'voucher': 'VOU-573925', 'timeLeft': '4h:Left', 'phone': '0700654321', 'status': 'active'},
      {'voucher': 'VOU-573926', 'timeLeft': '2h:Left', 'phone': '0700567890', 'status': 'critical'},
    ];

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Detailed Breakdown',
            style: AppTextStyles.h4,
          ),
          
          const SizedBox(height: AppDimensions.marginL),
          
          ...users.map((user) => _buildUserItem(
            user['voucher'] as String,
            user['timeLeft'] as String,
            user['phone'] as String,
            user['status'] as String,
          )),
        ],
      ),
    );
  }

  Widget _buildUserItem(String voucher, String timeLeft, String phone, String status) {
    Color statusColor;
    switch (status) {
      case 'active':
        statusColor = AppColors.success;
        break;
      case 'warning':
        statusColor = AppColors.warning;
        break;
      case 'critical':
        statusColor = AppColors.error;
        break;
      default:
        statusColor = AppColors.textSecondary;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginM),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.textSecondary,
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: Text(
                'User',
                style: TextStyle(
                  color: AppColors.surface,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          const SizedBox(width: AppDimensions.marginM),

          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  voucher,
                  style: AppTextStyles.labelMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: statusColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: AppDimensions.marginXS),
                    Text(
                      'Time: $timeLeft',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(width: AppDimensions.marginS),

          Flexible(
            flex: 1,
            child: Text(
              phone,
              style: AppTextStyles.labelSmall.copyWith(
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.right,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: ElevatedButton.icon(
            onPressed: () {
              // TODO: Implement export functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Export report functionality will be implemented'),
                  backgroundColor: AppColors.primary,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.surface,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            icon: const Icon(
              Icons.download,
              size: AppDimensions.iconS,
            ),
            label: Text(
              'Export Report',
              style: AppTextStyles.labelLarge.copyWith(
                color: AppColors.surface,
              ),
            ),
          ),
        ),

        const SizedBox(height: AppDimensions.marginM),

        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: OutlinedButton.icon(
            onPressed: () {
              // TODO: Implement view logs functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('View logs functionality will be implemented'),
                  backgroundColor: AppColors.textSecondary,
                ),
              );
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.textPrimary,
              side: const BorderSide(
                color: AppColors.divider,
                width: 1,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            icon: const Icon(
              Icons.description,
              size: AppDimensions.iconS,
            ),
            label: Text(
              'View Logs',
              style: AppTextStyles.labelLarge.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Custom painter for the activity chart
class _ActivityChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final pointPaint = Paint()
      ..color = AppColors.primary
      ..style = PaintingStyle.fill;

    // Mock chart data (representing hourly activity)
    final data = [12, 18, 24, 28, 30, 28, 32];
    final maxValue = data.reduce((a, b) => a > b ? a : b).toDouble();
    final stepWidth = size.width / (data.length - 1);

    final path = Path();
    final points = <Offset>[];

    for (int i = 0; i < data.length; i++) {
      final x = i * stepWidth;
      final y = size.height - 40 - ((data[i] / maxValue) * (size.height - 80));

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }

      points.add(Offset(x, y));
    }

    // Draw the line
    canvas.drawPath(path, paint);

    // Draw the points
    for (final point in points) {
      canvas.drawCircle(point, 4, pointPaint);
    }

    // Draw time labels
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    final timeLabels = ['8AM', '9AM', '10AM', '11AM', '12PM', '1PM', '3PM'];
    for (int i = 0; i < timeLabels.length; i++) {
      textPainter.text = TextSpan(
        text: timeLabels[i],
        style: AppTextStyles.caption.copyWith(
          color: AppColors.textSecondary,
        ),
      );
      textPainter.layout();

      final x = (i * stepWidth) - (textPainter.width / 2);
      textPainter.paint(canvas, Offset(x, size.height - 20));
    }

    // Draw value labels on the left
    final valueLabels = ['0', '8', '16', '24', '32'];
    for (int i = 0; i < valueLabels.length; i++) {
      textPainter.text = TextSpan(
        text: valueLabels[i],
        style: AppTextStyles.caption.copyWith(
          color: AppColors.textSecondary,
        ),
      );
      textPainter.layout();

      final y = size.height - 40 - (i * (size.height - 80) / (valueLabels.length - 1)) - (textPainter.height / 2);
      textPainter.paint(canvas, Offset(0, y));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
