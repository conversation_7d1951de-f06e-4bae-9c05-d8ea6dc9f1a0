enum TransactionStatus {
  active,
  expired,
  pending,
  completed,
}

enum TransactionType {
  oneDayPass,
  threeDayPass,
  weeklyBasic,
  weeklyPremium,
  monthlyBasic,
  monthlyPremium,
  annualBasic,
  annualPremium,
}

class TransactionDetail {
  final String id;
  final String customerInfo; // email or phone
  final double amount;
  final TransactionType type;
  final TransactionStatus status;
  final DateTime createdAt;
  final String? description;

  TransactionDetail({
    required this.id,
    required this.customerInfo,
    required this.amount,
    required this.type,
    required this.status,
    required this.createdAt,
    this.description,
  });

  String get typeDisplayName {
    switch (type) {
      case TransactionType.oneDayPass:
        return '1-Day Pass';
      case TransactionType.threeDayPass:
        return '3-Day Pass';
      case TransactionType.weeklyBasic:
        return 'Weekly Basic';
      case TransactionType.weeklyPremium:
        return 'Weekly Premium';
      case TransactionType.monthlyBasic:
        return 'Monthly Basic';
      case TransactionType.monthlyPremium:
        return 'Monthly Premium';
      case TransactionType.annualBasic:
        return 'Annual Basic';
      case TransactionType.annualPremium:
        return 'Annual Premium';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case TransactionStatus.active:
        return 'Active';
      case TransactionStatus.expired:
        return 'Expired';
      case TransactionStatus.pending:
        return 'Pending';
      case TransactionStatus.completed:
        return 'Completed';
    }
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} days ago';
      } else {
        return '${(difference.inDays / 7).floor()} weeks ago';
      }
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String get timeDisplay {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays == 0) {
      // Today - show time
      final hour = createdAt.hour;
      final minute = createdAt.minute.toString().padLeft(2, '0');
      final period = hour >= 12 ? 'PM' : 'AM';
      final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
      return 'Today, $displayHour:$minute $period';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inHours < 48) {
      return timeAgo;
    } else {
      return timeAgo;
    }
  }

  bool isToday() {
    final now = DateTime.now();
    return createdAt.year == now.year &&
        createdAt.month == now.month &&
        createdAt.day == now.day;
  }

  bool isYesterday() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return createdAt.year == yesterday.year &&
        createdAt.month == yesterday.month &&
        createdAt.day == yesterday.day;
  }

  bool isOlder() {
    final twoDaysAgo = DateTime.now().subtract(const Duration(days: 2));
    return createdAt.isBefore(twoDaysAgo);
  }
}

class TransactionData {
  static List<TransactionDetail> getMockTransactions() {
    final now = DateTime.now();
    
    return [
      // Today's transactions
      TransactionDetail(
        id: 'TXN-001',
        customerInfo: '<EMAIL>',
        amount: 5.99,
        type: TransactionType.oneDayPass,
        status: TransactionStatus.active,
        createdAt: now.subtract(const Duration(hours: 2)),
      ),
      TransactionDetail(
        id: 'TXN-002',
        customerInfo: '+1 (555) 123-4567',
        amount: 19.99,
        type: TransactionType.weeklyPremium,
        status: TransactionStatus.active,
        createdAt: now.subtract(const Duration(hours: 5, minutes: 37)),
      ),
      
      // Yesterday's transactions
      TransactionDetail(
        id: 'TXN-003',
        customerInfo: '<EMAIL>',
        amount: 29.99,
        type: TransactionType.monthlyBasic,
        status: TransactionStatus.active,
        createdAt: now.subtract(const Duration(days: 1, hours: 3)),
      ),
      TransactionDetail(
        id: 'TXN-004',
        customerInfo: '+1 (555) 987-6543',
        amount: 12.99,
        type: TransactionType.threeDayPass,
        status: TransactionStatus.active,
        createdAt: now.subtract(const Duration(days: 1, hours: 8)),
      ),
      
      // Older transactions
      TransactionDetail(
        id: 'TXN-005',
        customerInfo: '<EMAIL>',
        amount: 14.99,
        type: TransactionType.weeklyBasic,
        status: TransactionStatus.expired,
        createdAt: now.subtract(const Duration(days: 2, hours: 4)),
      ),
      TransactionDetail(
        id: 'TXN-006',
        customerInfo: '+1 (555) 456-7890',
        amount: 5.99,
        type: TransactionType.oneDayPass,
        status: TransactionStatus.expired,
        createdAt: now.subtract(const Duration(days: 3, hours: 2)),
      ),
      TransactionDetail(
        id: 'TXN-007',
        customerInfo: '<EMAIL>',
        amount: 49.99,
        type: TransactionType.monthlyPremium,
        status: TransactionStatus.active,
        createdAt: now.subtract(const Duration(days: 5)),
      ),
      TransactionDetail(
        id: 'TXN-008',
        customerInfo: '+1 (555) 234-5678',
        amount: 99.99,
        type: TransactionType.annualBasic,
        status: TransactionStatus.active,
        createdAt: now.subtract(const Duration(days: 7)),
      ),
    ];
  }
}
