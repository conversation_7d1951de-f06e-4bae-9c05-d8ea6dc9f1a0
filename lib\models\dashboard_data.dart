import 'active_users_data.dart';

class DashboardStats {
  final double salesToday;
  final double salesGrowth;
  final int vouchersSold;
  final int vouchersGrowth;
  final int activeUsers;
  final int availableVouchers;
  final int expiringVouchers;

  DashboardStats({
    required this.salesToday,
    required this.salesGrowth,
    required this.vouchersSold,
    required this.vouchersGrowth,
    required this.activeUsers,
    required this.availableVouchers,
    required this.expiringVouchers,
  });
}

class EarningsData {
  final String day;
  final double amount;

  EarningsData({
    required this.day,
    required this.amount,
  });
}

class Transaction {
  final String id;
  final String type;
  final double amount;
  final String timestamp;
  final String description;

  Transaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.timestamp,
    required this.description,
  });
}

class DashboardData {
  final DashboardStats stats;
  final List<EarningsData> earningsData;
  final List<Transaction> recentTransactions;

  DashboardData({
    required this.stats,
    required this.earningsData,
    required this.recentTransactions,
  });

  // Mock data for demonstration
  static DashboardData getMockData() {
    return DashboardData(
      stats: DashboardStats(
        salesToday: 1245.0,
        salesGrowth: 12.0,
        vouchersSold: 37,
        vouchersGrowth: 5,
        activeUsers: ActiveUsersData.getTotalActiveUsers(),
        availableVouchers: 143,
        expiringVouchers: 8,
      ),
      earningsData: [
        EarningsData(day: 'Mon', amount: 850),
        EarningsData(day: 'Tue', amount: 920),
        EarningsData(day: 'Wed', amount: 780),
        EarningsData(day: 'Thu', amount: 1050),
        EarningsData(day: 'Fri', amount: 1180),
        EarningsData(day: 'Sat', amount: 1350),
        EarningsData(day: 'Sun', amount: 1245),
      ],
      recentTransactions: [
        Transaction(
          id: 'VOU-457811',
          type: '1-Day Pass',
          amount: 5.99,
          timestamp: '2h ago',
          description: '1-Day Pass',
        ),
        Transaction(
          id: 'VOU-457812',
          type: 'Weekly Premium',
          amount: 19.99,
          timestamp: 'Today, 10:23 AM',
          description: 'Weekly Premium',
        ),
        Transaction(
          id: 'VOU-457813',
          type: 'Monthly Basic',
          amount: 29.99,
          timestamp: 'Yesterday',
          description: 'Monthly Basic',
        ),
        Transaction(
          id: 'VOU-457814',
          type: '3-Day Pass',
          amount: 12.99,
          timestamp: 'Yesterday',
          description: '3-Day Pass',
        ),
      ],
    );
  }
}
