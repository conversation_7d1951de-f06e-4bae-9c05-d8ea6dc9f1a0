import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_dimensions.dart';
import '../../models/settings_data.dart';

class VoucherPlanItem extends StatelessWidget {
  final VoucherPlan plan;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const VoucherPlanItem({
    super.key,
    required this.plan,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      margin: const EdgeInsets.only(bottom: AppDimensions.marginM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          // Plan details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  plan.name,
                  style: AppTextStyles.labelLarge,
                ),
                const SizedBox(height: AppDimensions.marginXS),
                Text(
                  plan.durationText,
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ),
          
          // Price
          Text(
            '\$${plan.price.toStringAsFixed(2)}',
            style: AppTextStyles.labelLarge,
          ),
          
          const SizedBox(width: AppDimensions.marginM),
          
          // Edit button
          GestureDetector(
            onTap: onEdit,
            child: Container(
              padding: const EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: const Icon(
                Icons.edit_outlined,
                size: AppDimensions.iconS,
                color: AppColors.primary,
              ),
            ),
          ),
          
          const SizedBox(width: AppDimensions.marginS),
          
          // Delete button
          GestureDetector(
            onTap: onDelete,
            child: Container(
              padding: const EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.error.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: const Icon(
                Icons.delete_outline,
                size: AppDimensions.iconS,
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
