class BusinessInfo {
  final String businessName;
  final String contactPhone;
  final String contactEmail;
  final String? logoUrl;

  BusinessInfo({
    required this.businessName,
    required this.contactPhone,
    required this.contactEmail,
    this.logoUrl,
  });

  BusinessInfo copyWith({
    String? businessName,
    String? contactPhone,
    String? contactEmail,
    String? logoUrl,
  }) {
    return BusinessInfo(
      businessName: businessName ?? this.businessName,
      contactPhone: contactPhone ?? this.contactPhone,
      contactEmail: contactEmail ?? this.contactEmail,
      logoUrl: logoUrl ?? this.logoUrl,
    );
  }
}

class VoucherPlan {
  final String id;
  final String name;
  final double price;
  final int durationDays;
  final String description;

  VoucherPlan({
    required this.id,
    required this.name,
    required this.price,
    required this.durationDays,
    required this.description,
  });

  String get durationText {
    if (durationDays == 1) {
      return 'Duration: 1 day';
    } else if (durationDays < 30) {
      return 'Duration: $durationDays days';
    } else if (durationDays == 30) {
      return 'Duration: 30 days';
    } else if (durationDays < 365) {
      final months = (durationDays / 30).round();
      return 'Duration: $months month${months > 1 ? 's' : ''}';
    } else {
      final years = (durationDays / 365).round();
      return 'Duration: $years year${years > 1 ? 's' : ''}';
    }
  }

  VoucherPlan copyWith({
    String? id,
    String? name,
    double? price,
    int? durationDays,
    String? description,
  }) {
    return VoucherPlan(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      durationDays: durationDays ?? this.durationDays,
      description: description ?? this.description,
    );
  }
}

class SettingsData {
  static BusinessInfo getMockBusinessInfo() {
    return BusinessInfo(
      businessName: 'WiFi Connect Pro',
      contactPhone: '+****************',
      contactEmail: '<EMAIL>',
      logoUrl: null, // Will use placeholder
    );
  }

  static List<VoucherPlan> getMockVoucherPlans() {
    return [
      VoucherPlan(
        id: 'plan_1',
        name: '1-Day Pass',
        price: 5.99,
        durationDays: 1,
        description: 'Basic internet access for 1 day',
      ),
      VoucherPlan(
        id: 'plan_2',
        name: 'Weekly Premium',
        price: 19.99,
        durationDays: 7,
        description: 'Premium internet access for 7 days',
      ),
      VoucherPlan(
        id: 'plan_3',
        name: 'Monthly Basic',
        price: 29.99,
        durationDays: 30,
        description: 'Basic internet access for 30 days',
      ),
      VoucherPlan(
        id: 'plan_4',
        name: '3-Day Pass',
        price: 12.99,
        durationDays: 3,
        description: 'Basic internet access for 3 days',
      ),
    ];
  }
}
