import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_dimensions.dart';
import '../../models/transaction_data.dart';

class TransactionDetailItem extends StatelessWidget {
  final TransactionDetail transaction;
  final VoidCallback? onTap;

  const TransactionDetailItem({
    super.key,
    required this.transaction,
    this.onTap,
  });

  Color get _statusColor {
    switch (transaction.status) {
      case TransactionStatus.active:
        return AppColors.success;
      case TransactionStatus.expired:
        return AppColors.textSecondary;
      case TransactionStatus.pending:
        return AppColors.warning;
      case TransactionStatus.completed:
        return AppColors.primary;
    }
  }

  Color get _statusBackgroundColor {
    switch (transaction.status) {
      case TransactionStatus.active:
        return AppColors.success.withOpacity(0.1);
      case TransactionStatus.expired:
        return AppColors.textSecondary.withOpacity(0.1);
      case TransactionStatus.pending:
        return AppColors.warning.withOpacity(0.1);
      case TransactionStatus.completed:
        return AppColors.primary.withOpacity(0.1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: AppDimensions.marginM),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: AppColors.divider,
            width: 0.5,
          ),
        ),
        child: IntrinsicHeight(
          child: Row(
            children: [
              // Blue left border indicator
              Container(
                width: 4,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppDimensions.radiusM),
                    bottomLeft: Radius.circular(AppDimensions.radiusM),
                  ),
                ),
              ),
              
              // Transaction content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  child: Row(
                    children: [
                      // Left side - Customer info and product
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              transaction.customerInfo,
                              style: AppTextStyles.labelMedium,
                            ),
                            const SizedBox(height: AppDimensions.marginXS),
                            Row(
                              children: [
                                Icon(
                                  Icons.verified_outlined,
                                  size: AppDimensions.iconXS,
                                  color: AppColors.textSecondary,
                                ),
                                const SizedBox(width: AppDimensions.marginXS),
                                Text(
                                  transaction.typeDisplayName,
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                const SizedBox(width: AppDimensions.marginS),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: AppDimensions.paddingS,
                                    vertical: AppDimensions.paddingXS,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _statusBackgroundColor,
                                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                                  ),
                                  child: Text(
                                    transaction.statusDisplayName,
                                    style: AppTextStyles.caption.copyWith(
                                      color: _statusColor,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      
                      // Right side - Amount and time
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '\$${transaction.amount.toStringAsFixed(2)}',
                            style: AppTextStyles.labelLarge,
                          ),
                          const SizedBox(height: AppDimensions.marginXS),
                          Text(
                            transaction.timeDisplay,
                            style: AppTextStyles.bodySmall,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
