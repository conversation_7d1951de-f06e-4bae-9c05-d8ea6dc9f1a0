class WiFiPlan {
  final String id;
  final String name;
  final double price;
  final int duration;
  final String durationUnit; // 'hours', 'days', 'weeks', 'months'
  final String? dataLimit; // e.g., '1GB', 'unlimited', null

  WiFiPlan({
    required this.id,
    required this.name,
    required this.price,
    required this.duration,
    required this.durationUnit,
    this.dataLimit,
  });

  String get durationDisplay {
    if (duration == 1) {
      switch (durationUnit) {
        case 'hours':
          return '1 hour';
        case 'days':
          return '1 day';
        case 'weeks':
          return '1 week';
        case 'months':
          return '1 month';
        default:
          return '$duration $durationUnit';
      }
    } else {
      return '$duration $durationUnit';
    }
  }

  String get dataLimitDisplay {
    if (dataLimit == null || dataLimit!.isEmpty) {
      return 'No data limit';
    }
    if (dataLimit!.toLowerCase() == 'unlimited') {
      return 'Unlimited data';
    }
    return '$dataLimit data limit';
  }

  WiFiPlan copyWith({
    String? id,
    String? name,
    double? price,
    int? duration,
    String? durationUnit,
    String? dataLimit,
  }) {
    return WiFiPlan(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      duration: duration ?? this.duration,
      durationUnit: durationUnit ?? this.durationUnit,
      dataLimit: dataLimit ?? this.dataLimit,
    );
  }
}

class WiFiPlanData {
  static List<WiFiPlan> getDefaultPlans() {
    return [
      WiFiPlan(
        id: 'plan_1',
        name: 'Basic 1hr',
        price: 5.99,
        duration: 1,
        durationUnit: 'hours',
        dataLimit: '1GB',
      ),
      WiFiPlan(
        id: 'plan_2',
        name: 'Premium 24hr',
        price: 19.99,
        duration: 24,
        durationUnit: 'hours',
        dataLimit: 'unlimited',
      ),
    ];
  }
}
