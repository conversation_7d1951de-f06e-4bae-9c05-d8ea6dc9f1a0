import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/settings_data.dart';
import '../widgets/common/voucher_plan_item.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late BusinessInfo _businessInfo;
  late List<VoucherPlan> _voucherPlans;
  
  final TextEditingController _businessNameController = TextEditingController();
  final TextEditingController _contactPhoneController = TextEditingController();
  final TextEditingController _contactEmailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _businessInfo = SettingsData.getMockBusinessInfo();
    _voucherPlans = SettingsData.getMockVoucherPlans();
    
    _businessNameController.text = _businessInfo.businessName;
    _contactPhoneController.text = _businessInfo.contactPhone;
    _contactEmailController.text = _businessInfo.contactEmail;
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _contactPhoneController.dispose();
    _contactEmailController.dispose();
    super.dispose();
  }

  void _onChangeLogo() {
    // TODO: Implement logo change functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Change logo functionality will be implemented'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _onEditPlan(VoucherPlan plan) {
    // TODO: Implement edit plan functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit ${plan.name} functionality will be implemented'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _onDeletePlan(VoucherPlan plan) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Delete Plan', style: AppTextStyles.h4),
          content: Text(
            'Are you sure you want to delete "${plan.name}"?',
            style: AppTextStyles.bodyMedium,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _voucherPlans.removeWhere((p) => p.id == plan.id);
                });
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${plan.name} deleted'),
                    backgroundColor: AppColors.error,
                  ),
                );
              },
              child: Text(
                'Delete',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.error,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _onAddNewPlan() {
    // TODO: Implement add new plan functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add new plan functionality will be implemented'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _onLogout() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Logout', style: AppTextStyles.h4),
          content: Text(
            'Are you sure you want to logout?',
            style: AppTextStyles.bodyMedium,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Implement actual logout functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Logout functionality will be implemented'),
                    backgroundColor: AppColors.error,
                  ),
                );
              },
              child: Text(
                'Logout',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.error,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(),
              
              const SizedBox(height: AppDimensions.marginL),
              
              // Business Info Section
              _buildBusinessInfoSection(),
              
              const SizedBox(height: AppDimensions.marginXL),
              
              // Voucher Plans Section
              _buildVoucherPlansSection(),
              
              const SizedBox(height: AppDimensions.marginXL),
              
              // Logout Button
              _buildLogoutButton(),
              
              const SizedBox(height: AppDimensions.marginXL),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Settings',
            style: AppTextStyles.h2,
          ),
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.textSecondary,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.person,
              color: AppColors.surface,
              size: AppDimensions.iconM,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessInfoSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Business Info',
            style: AppTextStyles.h4,
          ),
          const SizedBox(height: AppDimensions.marginL),
          
          // Logo section
          Center(
            child: Column(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.primary,
                        AppColors.primary.withOpacity(0.7),
                      ],
                    ),
                  ),
                  child: const Icon(
                    Icons.business,
                    color: AppColors.surface,
                    size: 40,
                  ),
                ),
                const SizedBox(height: AppDimensions.marginS),
                GestureDetector(
                  onTap: _onChangeLogo,
                  child: Text(
                    'Change Logo',
                    style: AppTextStyles.labelMedium.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppDimensions.marginL),
          
          // Business Name
          Text(
            'Business Name',
            style: AppTextStyles.labelMedium,
          ),
          const SizedBox(height: AppDimensions.marginS),
          _buildTextField(_businessNameController),
          
          const SizedBox(height: AppDimensions.marginM),
          
          // Contact Phone
          Text(
            'Contact Phone',
            style: AppTextStyles.labelMedium,
          ),
          const SizedBox(height: AppDimensions.marginS),
          _buildTextField(_contactPhoneController),
          
          const SizedBox(height: AppDimensions.marginM),
          
          // Contact Email
          Text(
            'Contact Email',
            style: AppTextStyles.labelMedium,
          ),
          const SizedBox(height: AppDimensions.marginS),
          _buildTextField(_contactEmailController),
        ],
      ),
    );
  }

  Widget _buildTextField(TextEditingController controller) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: TextField(
        controller: controller,
        style: AppTextStyles.bodyMedium,
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(AppDimensions.paddingM),
        ),
      ),
    );
  }

  Widget _buildVoucherPlansSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Voucher Plans',
            style: AppTextStyles.h4,
          ),
          const SizedBox(height: AppDimensions.marginL),
          
          // Voucher plans list
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _voucherPlans.length,
            itemBuilder: (context, index) {
              final plan = _voucherPlans[index];
              return VoucherPlanItem(
                plan: plan,
                onEdit: () => _onEditPlan(plan),
                onDelete: () => _onDeletePlan(plan),
              );
            },
          ),
          
          // Add New Plan button
          SizedBox(
            width: double.infinity,
            height: AppDimensions.buttonHeight,
            child: ElevatedButton.icon(
              onPressed: _onAddNewPlan,
              icon: const Icon(
                Icons.add,
                color: AppColors.surface,
                size: AppDimensions.iconS,
              ),
              label: Text(
                'Add New Plan',
                style: AppTextStyles.labelLarge.copyWith(
                  color: AppColors.surface,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.surface,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: SizedBox(
        width: double.infinity,
        height: AppDimensions.buttonHeight,
        child: ElevatedButton(
          onPressed: _onLogout,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.error,
            foregroundColor: AppColors.surface,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
          ),
          child: Text(
            'Logout',
            style: AppTextStyles.labelLarge.copyWith(
              color: AppColors.surface,
            ),
          ),
        ),
      ),
    );
  }
}
